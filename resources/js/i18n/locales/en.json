{"nav": {"brand": "CreateYourRun", "generateActivity": "Generate Activity", "export": "Export", "getTokens": "Get Tokens", "profile": "Profile", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "footer": {"copyright": "© CreateYourRun. All rights reserved."}, "home": {"title": "Welcome to CreateYourRun", "subtitle": "Create virtual running activities and export them to your favorite platforms", "description": "Create authentic running and cycling routes with realistic pace variations that pass Strava's analysis algorithms.", "getStarted": "Get Started", "newFeature": {"title": "New! All-in-One Creation", "description": "Create routes and configure activities in a single step - faster and easier!", "button": "Try All-in-One Creator"}, "features": {"createRoutes": {"title": "Create Routes", "description": "Design custom running routes on an interactive map. Add waypoints, adjust the path, and create the perfect route.", "button": "Start Creating"}, "generateActivities": {"title": "Generate Activities", "description": "Turn your routes into realistic activities with customizable pace, heart rate, cadence, and other metrics.", "button": "Generate Now"}, "exportActivities": {"title": "Export Activities", "description": "Export your generated activities in various formats (GPX, TCX) to upload to Strava and other fitness platforms.", "button": "Export"}}, "whyUse": {"title": "Why Use CreateYourRun?", "reason1": "Test new running routes virtually before trying them in real life", "reason2": "Keep your training data consistent during device transitions", "reason3": "Maintain your activity streak when traveling or injured", "reason4": "Compare performance metrics across different routes", "reason5": "Share route ideas with friends and running groups"}}, "auth": {"login": {"title": "Login to Your Account", "email": "Email Address", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "submit": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "signUp": "Sign up here"}, "register": {"title": "Create Your CreateYourRun Account", "name": "Full Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "termsAgreement": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "submit": "Create Account", "submitting": "Creating Account...", "hasAccount": "Already have an account?", "signIn": "<PERSON><PERSON>"}, "errors": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "nameRequired": "Name is required", "passwordMismatch": "Passwords do not match", "registrationFailed": "Registration failed. Please try again."}}, "activity": {"generator": {"title": "Generate Activity", "routeCreator": "Route Creator", "searchLocation": "Search for a location...", "search": "Search", "aligning": "Aligning...", "clear": "Clear", "activitySummary": "Activity Summary", "distance": "Distance", "duration": "Duration", "avgPace": "Avg Pace", "avgHeartRate": "Avg Heart Rate", "continueToExport": "Continue to Export", "configureMessage": "Configure your activity parameters and click Generate", "activityParameters": "Activity Parameters", "activityName": "Activity Name", "activityType": "Activity Type", "run": "Run", "bike": "Bike", "hike": "Hike", "activityDate": "Activity Date", "startTime": "Start Time", "pace": "Pace (min/km)", "paceDisplay": "(This is how it will appear in Strava)", "paceVariability": "Pace Variability (%)", "avgHeartRateLabel": "Avg Heart Rate (bpm)", "cadence": "Cadence (spm)", "generate": "Generate", "createRouteFirst": "Please create a route first by adding points on the map", "alignToBikeRoutes": "Align to Bike Routes", "alignToRunningPaths": "Align to Running Paths", "alignToWalkingPaths": "Align to Walking Paths", "alignToRoad": "Align to Road", "alignToBikeTooltip": "Snap route to bike lanes, cycling paths, and bike-friendly roads using Mapbox cycling profile", "alignToRunningTooltip": "Snap route to pedestrian paths, sidewalks, and running-friendly routes using Mapbox walking profile", "alignToWalkingTooltip": "Snap route to walking paths, trails, and pedestrian-friendly routes using Mapbox walking profile", "alignToRoadTooltip": "Snap route to roads and paths using Mapbox"}, "export": {"title": "Export Activity", "activitySummary": "Activity Summary", "distance": "Distance", "duration": "Duration", "avgPace": "Avg Pace", "avgHeartRate": "Avg Heart Rate", "noActivityData": "No activity data available", "generateActivity": "Generate an Activity", "whatNext": "What Next?", "uploadToStrava": {"title": "Upload to Strava", "description": "Export your activity and upload it to Strava", "link": "Go to Strava Upload"}, "createAnother": {"title": "Create Another Activity", "description": "Generate a new activity using the same or a different route", "button": "Create Another Activity"}, "designNew": {"title": "Design a New Route", "description": "Go back to the route creator to design a new running route", "button": "Design New Route"}, "exportOptions": "Export Options", "fileFormat": "File Format", "dataToInclude": "Data to Include", "heartRate": "Heart Rate", "cadence": "<PERSON><PERSON>", "elevation": "Elevation", "fileName": "File Name", "downloadFile": "Download File", "tokensRemaining": "You have {count} token{plural} remaining", "authErrors": {"loginRequired": "You must be logged in to export activities", "tokensRequired": "You need tokens to export activities. Please purchase more tokens.", "authError": "Authentication error. Please login again.", "activityNotFound": "Activity not found in database. Please try refreshing the page.", "tokenRequired": "You must be logged in with available tokens to export activities"}}}, "tokens": {"purchase": {"title": "Purchase Tokens", "currentBalance": "Current token balance:", "tokens": "tokens", "selectPackage": "Select a package:", "currency": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Payment", "securePayment": "Secure payment via PayOS", "package": "Package", "payWithPayOS": "Pay with PayOS", "creatingPaymentLink": "Creating Payment Link...", "redirectMessage": "You will be redirected to PayOS secure payment page", "paymentSuccessful": "Payment Successful!", "tokensAdded": "Your tokens have been added to your account.", "purchaseMore": "Purchase More Tokens", "paymentCancelled": "Payment Cancelled", "paymentCancelledMessage": "Your payment was cancelled. No charges were made.", "tryAgain": "Try Again", "selectPackageMessage": "Please select a package to continue", "paymentCompleted": "Payment completed successfully! Your tokens have been added to your account.", "paymentCancelledStatus": "Payment was cancelled. No charges were made.", "failedToLoad": "Failed to load packages. Please refresh the page.", "paymentLinkError": "Failed to create payment link. Please try again."}}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "accountStats": "Account Statistics", "name": "Name", "email": "Email", "memberSince": "Member Since", "activitiesCreated": "Activities Created", "availableTokens": "Available Tokens", "tokensDescription": "Tokens are used to create and export activities", "purchaseMoreTokens": "Purchase More Tokens", "loginRequired": "Please log in to view your profile.", "loginButton": "<PERSON><PERSON>"}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success!", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "language": {"switch": "Switch Language", "english": "English", "vietnamese": "Tiếng <PERSON>"}}