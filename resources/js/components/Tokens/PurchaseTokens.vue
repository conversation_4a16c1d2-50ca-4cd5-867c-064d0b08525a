<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">{{ $t('tokens.purchase.title') }}</h1>

        <div class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
            <div v-if="user" class="mb-6">
                <p class="text-strava-grayMedium mb-2">{{ $t('tokens.purchase.currentBalance') }}</p>
                <p class="text-3xl font-bold text-strava-gray">{{ user.tokens }} <span class="text-xl">{{ $t('tokens.purchase.tokens') }}</span></p>
            </div>

            <div v-if="message" class="p-4 mb-6" :class="messageClass">
                <p>{{ message }}</p>
            </div>

            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-4 sm:space-y-0">
                <h2 class="text-xl font-bold text-strava-gray">{{ $t('tokens.purchase.selectPackage') }}</h2>

                <!-- Currency Switcher -->
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-strava-grayMedium hidden sm:inline">{{ $t('tokens.purchase.currency') }}:</span>
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button
                            @click="setCurrency('USD')"
                            :class="[
                                'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                                selectedCurrency === 'USD'
                                    ? 'bg-strava-orange text-white'
                                    : 'text-strava-gray hover:text-strava-orange'
                            ]"
                        >
                            USD
                        </button>
                        <button
                            @click="setCurrency('VND')"
                            :class="[
                                'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                                selectedCurrency === 'VND'
                                    ? 'bg-strava-orange text-white'
                                    : 'text-strava-gray hover:text-strava-orange'
                            ]"
                        >
                            VND
                        </button>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div
                    v-for="(pkg, index) in packages"
                    :key="index"
                    class="border rounded-lg p-4 text-center hover:shadow-lg transition cursor-pointer"
                    :class="{ 'border-strava-orange': selectedPackage === index, 'border-gray-200': selectedPackage !== index }"
                    @click="selectPackage(index)"
                >
                    <h3 class="text-lg font-bold text-strava-gray mb-2">{{ pkg.name }}</h3>
                    <p class="text-2xl font-bold text-strava-orange mb-1">{{ formatPrice(pkg) }}</p>
                    <p class="text-strava-grayLight mb-4">{{ pkg.tokens }} tokens</p>
                    <p class="text-xs text-strava-grayLight">{{ pkg.description }}</p>
                </div>
            </div>

            <!-- PayOS Payment Section -->
            <div v-if="selectedPackage !== null && !paymentCompleted" class="mb-6">
                <h3 class="text-lg font-bold text-strava-gray mb-2">{{ $t('tokens.purchase.payment') }}</h3>
                <div class="border border-gray-200 rounded-lg p-4">
                    <p class="text-strava-grayLight mb-4">{{ $t('tokens.purchase.securePayment') }}</p>

                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg" v-if="packages[selectedPackage]">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">{{ packages[selectedPackage].name }} {{ $t('tokens.purchase.package') }}</span>
                            <span class="font-bold text-lg">{{ formatPrice(packages[selectedPackage]) }}</span>
                        </div>
                        <p class="text-sm text-gray-600">{{ packages[selectedPackage].tokens }} {{ $t('tokens.purchase.tokens') }} • {{ packages[selectedPackage].description }}</p>
                    </div>

                    <div class="flex justify-center mb-4">
                        <button
                            @click="handlePayment"
                            class="bg-strava-orange text-white py-3 px-8 rounded hover:bg-strava-orangeLight text-lg font-semibold"
                            :disabled="loading"
                        >
                            {{ loading ? $t('tokens.purchase.creatingPaymentLink') : $t('tokens.purchase.payWithPayOS') }}
                        </button>
                    </div>

                    <p class="text-xs text-gray-500 text-center">
                        {{ $t('tokens.purchase.redirectMessage') }}
                    </p>
                </div>
            </div>

            <!-- Payment Success Message -->
            <div v-if="paymentCompleted" class="mb-6">
                <div class="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
                    <div class="text-green-600 text-4xl mb-4">✓</div>
                    <h3 class="text-lg font-bold text-green-800 mb-2">{{ $t('tokens.purchase.paymentSuccessful') }}</h3>
                    <p class="text-green-700 mb-4">{{ $t('tokens.purchase.tokensAdded') }}</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        {{ $t('tokens.purchase.purchaseMore') }}
                    </button>
                </div>
            </div>

            <!-- Payment Cancelled Message -->
            <div v-if="paymentCancelled" class="mb-6">
                <div class="text-center p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="text-yellow-600 text-4xl mb-4">⚠</div>
                    <h3 class="text-lg font-bold text-yellow-800 mb-2">{{ $t('tokens.purchase.paymentCancelled') }}</h3>
                    <p class="text-yellow-700 mb-4">{{ $t('tokens.purchase.paymentCancelledMessage') }}</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        {{ $t('tokens.purchase.tryAgain') }}
                    </button>
                </div>
            </div>



            <!-- Default Purchase Button (when no package selected) -->
            <div v-if="selectedPackage === null && !paymentCompleted && !paymentCancelled" class="flex justify-end">
                <p class="text-strava-grayLight">{{ $t('tokens.purchase.selectPackageMessage') }}</p>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import axios from 'axios';

export default {
    name: 'PurchaseTokens',
    setup() {
        const router = useRouter();
        const { t } = useI18n();
        const user = ref(null);
        const selectedPackage = ref(null);
        const message = ref('');
        const messageClass = ref('');
        const loading = ref(false);
        const packages = ref([]);

        // Payment state
        const paymentCompleted = ref(false);
        const paymentCancelled = ref(false);

        // Currency state
        const selectedCurrency = ref(localStorage.getItem('preferred_currency') || 'USD');

        const fetchUserData = async () => {
            try {
                const response = await axios.get('/api/user');
                user.value = response.data;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Redirect to login if not authenticated
                if (error.response && error.response.status === 401) {
                    router.push({ name: 'login' });
                }
            }
        };

        const fetchPackages = async () => {
            try {
                const response = await axios.get('/api/token-packages');
                packages.value = response.data.packages;
            } catch (error) {
                console.error('Error fetching packages:', error);
                message.value = t('tokens.purchase.failedToLoad');
                messageClass.value = 'text-red-600';
            }
        };

        // Check URL parameters for payment status on component mount
        const checkPaymentStatus = () => {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('success') === 'true') {
                paymentCompleted.value = true;
                message.value = t('tokens.purchase.paymentCompleted');
                messageClass.value = 'bg-green-100 text-green-700 border-l-4 border-green-500';

                // Refresh user data to reflect new token balance (webhook should have processed)
                setTimeout(async () => {
                    await fetchUserData();
                }, 2000);

                // Clean up URL parameters
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            } else if (urlParams.get('canceled') === 'true') {
                paymentCancelled.value = true;
                message.value = t('tokens.purchase.paymentCancelledStatus');
                messageClass.value = 'bg-yellow-100 text-yellow-700 border-l-4 border-yellow-500';

                // Clean up URL parameters
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            }
        };

        onMounted(async () => {
            await fetchUserData();
            await fetchPackages();
            checkPaymentStatus();
        });

        const selectPackage = (index) => {
            selectedPackage.value = index;
            // Reset payment state when selecting a new package
            resetPaymentState();
        };

        const handlePayment = async () => {
            if (selectedPackage.value === null) return;

            loading.value = true;
            message.value = '';

            try {
                const pkg = packages.value[selectedPackage.value];
                if (!pkg) {
                    throw new Error('Selected package not found');
                }

                const response = await axios.post('/api/payos/create-payment', {
                    package_type: pkg.type
                });

                if (response.data.success) {
                    const { checkoutUrl } = response.data.data;

                    // Redirect to PayOS hosted checkout page
                    window.location.href = checkoutUrl;
                } else {
                    throw new Error(response.data.message || 'Failed to create payment link');
                }

            } catch (error) {
                console.error('Payment link creation error:', error);
                message.value = error.response?.data?.message || t('tokens.purchase.paymentLinkError');
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
                loading.value = false;
            }
        };

        const resetPayment = () => {
            resetPaymentState();
            selectedPackage.value = null;
            message.value = '';
        };

        const resetPaymentState = () => {
            paymentCompleted.value = false;
            paymentCancelled.value = false;
        };

        // Currency functions
        const setCurrency = (currency) => {
            selectedCurrency.value = currency;
            localStorage.setItem('preferred_currency', currency);
        };

        const formatPrice = (pkg) => {
            if (selectedCurrency.value === 'VND') {
                // Use the amount field which is already in VND (smallest unit)
                const vndAmount = pkg.amount || (parseFloat(pkg.price) * 25000); // Fallback conversion rate
                return new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(vndAmount);
            } else {
                // Format USD price
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                }).format(parseFloat(pkg.price));
            }
        };

        return {
            user,
            packages,
            selectedPackage,
            message,
            messageClass,
            loading,
            paymentCompleted,
            paymentCancelled,
            selectedCurrency,
            selectPackage,
            handlePayment,
            resetPayment,
            setCurrency,
            formatPrice
        };
    }
}
</script>

<style>
/* Strava style customizations */
input:focus {
    outline: none;
    border-color: #FC4C02;
}
</style>
