<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create(['tokens' => 5]);
});

test('token packages endpoint returns packages with both USD and VND pricing', function () {
    $response = $this->getJson('/api/token-packages');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'packages' => [
                '*' => [
                    'type',
                    'name',
                    'tokens',
                    'price',
                    'description'
                ]
            ]
        ]);

    $packages = $response->json('packages');
    expect($packages)->toHaveCount(3);

    // Verify each package has the required fields for currency switching
    foreach ($packages as $package) {
        expect($package)->toHaveKeys(['type', 'name', 'tokens', 'price', 'description']);
        expect($package['price'])->toBeString(); // USD price as formatted string
    }
});

test('packages contain expected pricing structure for currency conversion', function () {
    $response = $this->getJson('/api/token-packages');
    $packages = $response->json('packages');

    // Check that basic package has expected structure
    $basicPackage = collect($packages)->firstWhere('type', 'basic');
    expect($basicPackage)->not->toBeNull();
    expect($basicPackage['price'])->toBe('0.60'); // USD formatted
    expect($basicPackage['tokens'])->toBe(1);

    // Check standard package
    $standardPackage = collect($packages)->firstWhere('type', 'standard');
    expect($standardPackage)->not->toBeNull();
    expect($standardPackage['price'])->toBe('4.50'); // USD formatted
    expect($standardPackage['tokens'])->toBe(10);

    // Check premium package
    $premiumPackage = collect($packages)->firstWhere('type', 'premium');
    expect($premiumPackage)->not->toBeNull();
    expect($premiumPackage['price'])->toBe('8.00'); // USD formatted
    expect($premiumPackage['tokens'])->toBe(20);
});

test('config contains VND amounts for currency conversion', function () {
    $packages = config('payos.packages');

    expect($packages)->toHaveKeys(['basic', 'standard', 'premium']);

    // Verify each package has amount field for VND pricing
    foreach ($packages as $package) {
        expect($package)->toHaveKeys(['name', 'tokens', 'price', 'amount', 'description']);
        expect($package['amount'])->toBeInt(); // VND amount in smallest unit
    }

    // Check specific VND amounts
    expect($packages['basic']['amount'])->toBe(15600);
    expect($packages['standard']['amount'])->toBe(115000);
    expect($packages['premium']['amount'])->toBe(210000);
});
